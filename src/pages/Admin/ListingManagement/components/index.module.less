.addListingForm{
  .colTitle{
    font-size: 16px;
    font-weight: bold;
    margin-top: 8px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    .questionIcon{
      color: #86909C;
    }
  }
  .productCard{
    height: 500px;
    overflow-y: auto;
    border: none;
    background-color: #F9FAFB;
    :global{
      .ant-card-body{
        padding: 16px;
      }
    }
  }
  .productInfo{
    display: flex;
    align-items: flex-start;
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
  }
  .formCard{
    &:last-child{
      margin-bottom: 0;
    }
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
    :global {
      .ant-form-item {
        margin-bottom: 16px;
      }
    }
    .formItem{
      :global{
        .ant-form-item-label {
          border-bottom: 1px solid #F2F3F5;
          margin-bottom: 16px !important;
          font-weight: bold;
        }
      }
    }
  }
  .promotionCard{ 
    padding: 16px;
    margin-bottom: 16px;
    border: 1px dashed #D0D5DD;
    background-color: #F9FAFB;
    border-radius: 8px;
  }
  .addPromotionButton{
    background-color: #F9FAFB;
    border-color: #EAECF0;
  }
}